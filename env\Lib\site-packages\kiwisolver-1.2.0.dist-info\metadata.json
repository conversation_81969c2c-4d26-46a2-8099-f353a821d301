{"generator": "bdist_wheel (0.26.0)", "summary": "A fast implementation of the Cassowary constraint solver", "classifiers": ["Programming Language :: Python", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.6", "Programming Language :: Python :: 3.7", "Programming Language :: Python :: 3.8", "Programming Language :: Python :: Implementation :: CPython"], "extensions": {"python.details": {"project_urls": {"Home": "https://github.com/nucleic/kiwi"}, "contacts": [{"email": "<EMAIL>", "name": "The Nucleic Development Team", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}}}, "license": "BSD", "metadata_version": "2.0", "name": "kiwisolver", "requires_python": ">=3.6", "version": "1.2.0"}