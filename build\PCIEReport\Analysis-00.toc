(['G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\PCIEReport.py'],
 ['G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport',
  'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport'],
 ['codecs'],
 ['G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks\\__pycache__',
  'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks',
  'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\__pycache__',
  'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 [],
 [],
 False,
 True,
 '3.7.0 (v3.7.0:1bf9cc5093, Jun 27 2018, 04:59:51) [MSC v.1914 64 bit (AMD64)]',
 [('pyi_rth_pkgres',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_win32comgenpy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_win32comgenpy.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_mpldata',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mpldata.py',
   'PYSOURCE'),
  ('pyi_rth_certifi',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_certifi.py',
   'PYSOURCE'),
  ('PCIEReport',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\PCIEReport.py',
   'PYSOURCE')],
 [('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ssl.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\textwrap.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\copy.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\gettext.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\struct.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_strptime.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\getopt.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\selectors.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_threading_local.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\contextlib.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\fnmatch.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\netrc.py',
   'PYMODULE'),
  ('stat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\stat.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\shlex.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\mimetypes.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\quopri.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\random.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\string.py',
   'PYMODULE'),
  ('posixpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\posixpath.py',
   'PYMODULE'),
  ('genericpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\genericpath.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\hashlib.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\optparse.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\header.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\bisect.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\client.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\contextvars.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\numbers.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.semaphore_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\semaphore_tracker.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pprint.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\doctest.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('ntpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ntpath.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pdb.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tty.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\token.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\plistlib.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pkgutil.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\runpy.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\glob.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\codeop.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\opcode.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\cmd.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\inspect.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ast.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\difflib.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\signal.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\subprocess.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('win32com',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.server.util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32traceutil',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win32com.util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('winerror',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('pywintypes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\sysconfig.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tarfile.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\bz2.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\py_compile.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('lib2to3.refactor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\refactor.py',
   'PYMODULE'),
  ('lib2to3.btm_matcher',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\btm_matcher.py',
   'PYMODULE'),
  ('lib2to3.btm_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\btm_utils.py',
   'PYMODULE'),
  ('lib2to3.pgen2.grammar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\grammar.py',
   'PYMODULE'),
  ('lib2to3.pygram',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pygram.py',
   'PYMODULE'),
  ('lib2to3.pytree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pytree.py',
   'PYMODULE'),
  ('lib2to3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\__init__.py',
   'PYMODULE'),
  ('lib2to3.patcomp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\patcomp.py',
   'PYMODULE'),
  ('lib2to3.pgen2.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\parse.py',
   'PYMODULE'),
  ('lib2to3.pgen2.literals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\literals.py',
   'PYMODULE'),
  ('lib2to3.fixer_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\fixer_util.py',
   'PYMODULE'),
  ('lib2to3.pgen2.token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\token.py',
   'PYMODULE'),
  ('lib2to3.pgen2.tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\tokenize.py',
   'PYMODULE'),
  ('lib2to3.pgen2.driver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\driver.py',
   'PYMODULE'),
  ('lib2to3.pgen2.pgen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\pgen.py',
   'PYMODULE'),
  ('lib2to3.pgen2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\pgen2\\__init__.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('site',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PyInstaller\\fake-modules\\site.py',
   'PYMODULE'),
  ('win32com.server',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.universal',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.client.build',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin.mfc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('commctrl',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pkg_resources',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.shell',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.six',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\six.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\uuid.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('netbios',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources.py31compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\py31compat.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('imp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\imp.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\typing.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\stringprep.py',
   'PYMODULE'),
  ('QaSummary',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\QaSummary.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.distutils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.fcompiler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\fcompiler\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.config_compiler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\config_compiler.py',
   'PYMODULE'),
  ('numpy.distutils.command',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.egg_info',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.extension',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('setuptools.command',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.install_scripts',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools.command.easy_install',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\easy_install.py',
   'PYMODULE'),
  ('setuptools.dist',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.config',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\config.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.depends',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.py33compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\py33compat.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\html\\parser.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_markupbase.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.pep425tags',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\pep425tags.py',
   'PYMODULE'),
  ('setuptools.glibc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\glibc.py',
   'PYMODULE'),
  ('setuptools.package_index',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\package_index.py',
   'PYMODULE'),
  ('setuptools.ssl_support',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\ssl_support.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.py27compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\py27compat.py',
   'PYMODULE'),
  ('setuptools.sandbox',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\sandbox.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\configparser.py',
   'PYMODULE'),
  ('distutils.command.build_scripts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.install',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\install.py',
   'PYMODULE'),
  ('distutils.command.install_scripts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.six',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\six.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools.namespaces',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\namespaces.py',
   'PYMODULE'),
  ('setuptools.version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools.extern',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.develop',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\develop.py',
   'PYMODULE'),
  ('setuptools.command.develop',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\develop.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_clib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\install_clib.py',
   'PYMODULE'),
  ('numpy.distutils.command.bdist_rpm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('distutils.command.bdist_rpm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools.command.bdist_rpm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('numpy.distutils.command.install',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools.command.install',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\setuptools\\command\\install.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_headers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('distutils.command.install_headers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_data',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\install_data.py',
   'PYMODULE'),
  ('distutils.command.install_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\install_data.py',
   'PYMODULE'),
  ('numpy.distutils.command.sdist',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_scripts',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_clib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('distutils.command.build_clib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_ext',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('numpy.distutils.system_info',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\system_info.py',
   'PYMODULE'),
  ('numpy.f2py',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py_testing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\f2py_testing.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_src',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\build_src.py',
   'PYMODULE'),
  ('numpy.distutils.conv_template',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\conv_template.py',
   'PYMODULE'),
  ('numpy.distutils.from_template',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\from_template.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\fileinput.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_py',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\build_py.py',
   'PYMODULE'),
  ('distutils.command.build_py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\build_py.py',
   'PYMODULE'),
  ('numpy.distutils.command.build',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('numpy.distutils.command.config',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\config.py',
   'PYMODULE'),
  ('numpy.distutils.command.autodist',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\command\\autodist.py',
   'PYMODULE'),
  ('numpy.distutils.mingw32ccompiler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\mingw32ccompiler.py',
   'PYMODULE'),
  ('distutils.msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.cygwinccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('distutils.command.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\command\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\cgi.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('numpy.distutils.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\core.py',
   'PYMODULE'),
  ('numpy.distutils.numpy_distribution',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\numpy_distribution.py',
   'PYMODULE'),
  ('numpy.distutils.extension',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\extension.py',
   'PYMODULE'),
  ('numpy.distutils.fcompiler.environment',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\fcompiler\\environment.py',
   'PYMODULE'),
  ('numpy.distutils.exec_command',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\exec_command.py',
   'PYMODULE'),
  ('numpy.distutils.misc_util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\misc_util.py',
   'PYMODULE'),
  ('curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dummy_threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('numpy.distutils.__config__',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\__config__.py',
   'PYMODULE'),
  ('numpy.distutils.npy_pkg_config',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\npy_pkg_config.py',
   'PYMODULE'),
  ('numpy.distutils.unixccompiler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('numpy.distutils.ccompiler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('numpy.distutils._shell_utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\_shell_utils.py',
   'PYMODULE'),
  ('pipes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pipes.py',
   'PYMODULE'),
  ('numpy.distutils.lib2def',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\lib2def.py',
   'PYMODULE'),
  ('numpy.distutils.log',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\distutils\\log.py',
   'PYMODULE'),
  ('numpy.testing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.dual',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\dual.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.ma',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.random',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.financial',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\financial.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.machar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pathlib.py',
   'PYMODULE'),
  ('numpy.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.__config__',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._globals',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('PIL.Image',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\fractions.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\colorsys.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._binary',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('SmartTest',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\SmartTest.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\csv.py',
   'PYMODULE'),
  ('ReadDisturb',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\ReadDisturb.py',
   'PYMODULE'),
  ('InitPerformanceTest',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\InitPerformanceTest.py',
   'PYMODULE'),
  ('H2Test',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\H2Test.py',
   'PYMODULE'),
  ('BurninTest',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\BurninTest.py',
   'PYMODULE'),
  ('summary',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\summary.py',
   'PYMODULE'),
  ('explandTest',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\explandTest.py',
   'PYMODULE'),
  ('reciveTest',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\reciveTest.py',
   'PYMODULE'),
  ('RebootSleepLaterPerformance',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\RebootSleepLaterPerformance.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.path',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_template',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_template.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_svg.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\afm.py',
   'PYMODULE'),
  ('pyparsing',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pyparsing.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.fontconfig_pattern',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.units',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('dateutil.tz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('six',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('dateutil',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil.easter',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.parser',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil._common',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_mixed',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_mixed.py',
   'PYMODULE'),
  ('matplotlib.tight_bbox',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tight_bbox.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_ps',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_ps.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib._text_layout',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_text_layout.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_pgf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_pgf.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_pdf.py',
   'PYMODULE'),
  ('matplotlib.type1font',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\type1font.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_pdf_ps',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\_backend_pdf_ps.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.text',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('matplotlib.tri.triangulation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri.triplot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\triplot.py',
   'PYMODULE'),
  ('matplotlib.tri.tripcolor',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri.trirefine',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri.triinterpolate',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri.tritools',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\tritools.py',
   'PYMODULE'),
  ('matplotlib.tri.tricontour',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\tricontour.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.blocking_input',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\blocking_input.py',
   'PYMODULE'),
  ('matplotlib.tri.trifinder',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tri\\trifinder.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.container',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.table',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.category',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.axes._subplots',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\axes\\_subplots.py',
   'PYMODULE'),
  ('matplotlib._layoutbox',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_layoutbox.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.tight_layout',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\tight_layout.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.docstring',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\docstring.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\cbook\\__init__.py',
   'PYMODULE'),
  ('matplotlib.cbook.deprecation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\cbook\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib.style',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.animation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\animation.py',
   'PYMODULE'),
  ('matplotlib._animation_data',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_animation_data.py',
   'PYMODULE'),
  ('matplotlib.image',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('certifi',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('matplotlib._version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('cycler',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\cycler.py',
   'PYMODULE'),
  ('InnerTest',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\InnerTest.py',
   'PYMODULE'),
  ('PerformanceDingRong',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\PerformanceDingRong.py',
   'PYMODULE'),
  ('PublicFuc',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\PublicFuc.py',
   'PYMODULE'),
  ('Performance',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\Performance.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('requests',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('requests.api',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('idna',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.intranges',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.idnadata',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname._implementation',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\_implementation.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ipaddress.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.request',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.connection',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.response',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.hooks',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.models',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.utils',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.__version__',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('requests.exceptions',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('chardet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langcyrillicmodel',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.enums',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.compat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\chardet\\compat.py',
   'PYMODULE'),
  ('urllib3',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._version',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('win32com.client',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tempfile.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\shutil.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('os',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\os.py',
   'PYMODULE')],
 [('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('python37.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\Scripts\\python37.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('pythoncom37.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pywin32_system32\\pythoncom37.dll',
   'BINARY'),
  ('pywintypes37.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pywin32_system32\\pywintypes37.dll',
   'BINARY'),
  ('libopenblas.NOIJJG62EMASZI6NYURL6JBKM4EVBGM7.gfortran-win_amd64.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\.libs\\libopenblas.NOIJJG62EMASZI6NYURL6JBKM4EVBGM7.gfortran-win_amd64.dll',
   'BINARY'),
  ('select',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\select.pyd',
   'EXTENSION'),
  ('_socket',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\_socket.pyd',
   'EXTENSION'),
  ('_ssl',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\_ssl.pyd',
   'EXTENSION'),
  ('pyexpat',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\pyexpat.pyd',
   'EXTENSION'),
  ('_hashlib',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\_hashlib.pyd',
   'EXTENSION'),
  ('_contextvars',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\_contextvars.pyd',
   'EXTENSION'),
  ('_decimal',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\_multiprocessing.pyd',
   'EXTENSION'),
  ('win32trace',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('_lzma',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\_bz2.pyd',
   'EXTENSION'),
  ('_win32sysloader',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32ui',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32api',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32com.shell.shell',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32wnet',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('unicodedata',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\unicodedata.pyd',
   'EXTENSION'),
  ('_elementtree',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\_elementtree.pyd',
   'EXTENSION'),
  ('_distutils_findvs',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\_distutils_findvs.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_tests',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_umath',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('win32pdh',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy.linalg.lapack_lite',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.mtrand',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._sfc64',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._philox',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_philox.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._pcg64',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._mt19937',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.bit_generator',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._generator',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._bounded_integers',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._common',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\random\\_common.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.fft._pocketfft_internal',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.linalg._umath_linalg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imagingtk',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\_imagingtk.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._webp',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\_webp.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imaging',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\PIL\\_imaging.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pymssql',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\pymssql.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_mssql',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\_mssql.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib._path',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_path.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib.ft2font',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\ft2font.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib._ttconv',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_ttconv.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\_tkinter.pyd',
   'EXTENSION'),
  ('matplotlib.backends._tkagg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\_tkagg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib.backends._backend_agg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib._contour',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_contour.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib._tri',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_tri.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib._qhull',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_qhull.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\kiwisolver.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib._image',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\_image.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-1_1-x64.dll',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\libssl-1_1-x64.dll',
   'BINARY'),
  ('libcrypto-1_1-x64.dll',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('mfc140u.dll',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('tk86t.dll',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\tk86t.dll',
   'BINARY'),
  ('tcl86t.dll',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\env\\Scripts\\tcl86t.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'G:\\TOOLWORKS\\AterEX\\PCIEReport\\PCIEReport\\build\\PCIEReport\\base_library.zip',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-dark.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('tcl\\word.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tk\\clrpick.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tzdata\\EST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('tcl\\tzdata\\ROK',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('tk\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.gif',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('Include\\pyconfig.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\Include\\pyconfig.h',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tk\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tk\\images\\logo.eps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-notebook.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-notebook.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\tzdata\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('tcl\\tzdata\\MST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.gif',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tk\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tk\\icons.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-dark-palette.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-dark-palette.mplstyle',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tk\\tk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\tzdata\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.gif',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tk\\text.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.gif',
   'DATA'),
  ('tcl\\tzdata\\NZ',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-ticks.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-ticks.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.ppm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.ppm',
   'DATA'),
  ('tcl\\tm.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-pastel.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-pastel.mplstyle',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tcl\\tzdata\\CET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\HST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tcl\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('tk\\listbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\tzdata\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-colorblind.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('tcl\\history.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\WET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tk\\choosedir.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\msgs\\kw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('tcl\\clock.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('lib2to3\\Grammar.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\Grammar.txt',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.ppm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.ppm',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('tk\\images\\README',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.gif',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-talk.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-deep.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-deep.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tk\\safetk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('tcl\\tzdata\\MET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\ct.raw.gz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\ct.raw.gz',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\ada.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\ada.png',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\tzdata\\PRC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tk\\menu.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\percent_bachelors_degrees_women_usa.csv',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\percent_bachelors_degrees_women_usa.csv',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tk\\tkfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\init.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('tcl\\safe.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('lib2to3\\PatternGrammar.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\PatternGrammar.txt',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-paper.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-paper.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.gif',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tk\\bgerror.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tk\\dialog.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_128.ppm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_128.ppm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tk\\focus.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-darkgrid.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-darkgrid.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\msgs\\te.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('tcl\\parray.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\GB',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('tk\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tk\\scrlbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tk\\mkpsenc.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\auto.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tk\\console.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\msgs\\lv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\package.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('tcl\\tzdata\\EET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tk\\license.terms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn.mplstyle',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.png',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('tk\\unsupported.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.gif',
   'DATA'),
  ('tcl\\msgs\\vi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('lib2to3\\tests\\data\\README',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\tests\\data\\README',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tk\\comdlg.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\tzdata\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\tzdata\\ROC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tk\\optMenu.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tk\\obsolete.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tk\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('lib2to3\\Grammar3.7.0.final.0.pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\Grammar3.7.0.final.0.pickle',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\tzdata\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tk\\tearoff.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tk\\iconlist.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-whitegrid.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-whitegrid.mplstyle',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\None_vs_nearest-pdf.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\None_vs_nearest-pdf.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-bright.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-bright.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-muted.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-muted.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\demodata.csv',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\demodata.csv',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-poster.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-poster.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-white.mplstyle',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-white.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tk\\megawidget.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tk\\palette.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\aapl.npz',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\aapl.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tk\\msgbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('lib2to3\\PatternGrammar3.7.0.final.0.pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lib2to3\\PatternGrammar3.7.0.final.0.pickle',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand_large.gif',
   'DATA'),
  ('certifi\\cacert.pem',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.gif',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.gif',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.gif',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tk\\xmfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'G:\\TOOLWORKS\\AterEX\\SsdReport\\SsdReport\\env\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA')],
 [])
